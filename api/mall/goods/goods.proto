syntax = "proto3";

package api.mall.goods;

option go_package = "teacher-mall/api/mall/goods;goods";
option java_multiple_files = true;
option java_package = "api.mall.goods";

import "google/api/annotations.proto";
import "openapiv3/annotations.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/any.proto";

service Goods {
  // 同步课商品学科学段.
  rpc GetStageSubjects(GetStageSubjectsRequest) returns (GetStageSubjectsReply){
    option (google.api.http) = {
      get : "/teacher-mall/mall/goods/stage-subjects"
    };
    option (openapi.v3.operation) = {
      summary: "系统课商品学科学段列表",
    };
  };

  // 获取正价商品页面视图信息.
  rpc GetGoodsView(GetGoodsViewReq) returns (GetGoodsViewReply) {
    option (google.api.http) = {
      get : "/teacher-mall/mall/goods/view"
    };
    option (openapi.v3.operation) ={
      summary: "获取正价商品页面视图信息",
    };
  };
  // 获取sku组商品列表.
  rpc GetSkuGroupGoodsList(GetSkuGroupGoodsListReq) returns (GetSkuGroupGoodsListReply) {
    option (google.api.http) = {
      get : "/teacher-mall/mall/sku-group/list"
    };
    option (openapi.v3.operation) ={
      summary: "获取正价商品页面视图信息",
    };
  };
  // 通过sku组创建商品.
  rpc CreateGoodsBySkuGroup(CreateGoodsBySkuGroupReq) returns (GoodItem) {
    option (google.api.http) = {
      post: "/teacher-mall/mall/sku-group/goods"
      body : "*"
    };
    option (openapi.v3.operation) ={
      summary: "通过sku组生成商品",
    };
  };
  // 获取团购商品列表.
  rpc GetGroupGoodsList(GetGroupGoodsListReq) returns (GetGroupGoodsListReply){
    option (google.api.http) = {
      get : "/teacher-mall/mall/goods/group-buying-list"
    };
    option (openapi.v3.operation) ={
      summary: "获取团购商品列表",
    };
  };
  // 获取商品详情.
  rpc GetGoodsInfo(GetGoodsInfoReq) returns (GetGoodsInfoReply){
    option (google.api.http) = {
      get : "/teacher-mall/mall/goods/info"
    };
    option (openapi.v3.operation) ={
      summary: "获取商品详情",
    };
  };
  // B端商品H5页.
  rpc getGoodList(GetGoodListReq) returns (GetGoodListReply){
    option (google.api.http) = {
      get : "/teacher-mall/mall/goods/user/:userId/list"
    };
    option (openapi.v3.operation) ={
      summary: "B端商品H5页",
    };
  };
  // 通过id获取商品详情.
  rpc goodsDetailByID(goodsDetailByIDReq) returns (goodsDetailByIDReply){
    option (google.api.http) = {
      get : "/teacher-mall/mall/goods/detail"
    };
    option (openapi.v3.operation) ={
      summary: "通过id获取商品详情",
    };
  };
  rpc goodsMergeCoupon(goodsMergeCouponReq) returns (goodsDetailByIDReply){
    option (google.api.http) = {
      post : "/teacher-mall/mall/goods/merge-coupon"
      body : "*"
    };
    option (openapi.v3.operation) ={
      summary: "商品合并优惠券生成新商品",
    };
  };
  rpc GetCombinationGoodsExtraInfo(GetCombinationGoodsInfoReq)
      returns (google.protobuf.Struct) {
    option (google.api.http) = {
      get : "/teacher-mall/mall/goods/combination-goods/{goodsId}/extra-info"
    };
    option (openapi.v3.operation) = {
      summary: "组合商品附加信息",
    };
  };
  rpc IsRepeatPayGoodId(IsRepeatPayGoodIdReq)
      returns (IsRepeatPayGoodIdReply) {
    option (google.api.http) = {
      get : "/teacher-mall/mall/goods/is-repeat-good"
    };
    option (openapi.v3.operation) = {
      summary: "是否重复支付商品id",
    };
  };
  // 获取教师端商品列表.
  rpc GetTeacherGoodsList(GetTeacherGoodsListReq)
      returns (google.protobuf.ListValue) {
    option (google.api.http) = {
      get : "/teacher-mall/mall/teacher/orders/good/list"
    };
    option (openapi.v3.operation) = {
      summary: "获取教师端商品列表",
    };
  };
  // 通过用户所在学校是否允许购买该商品.
  rpc CheckCanBuyGoodsBySchool(CheckCanBuyGoodsBySchoolReq)
      returns (CheckCanBuyGoodsBySchoolRes) {
    option (google.api.http) = {
      post : "/teacher-mall/mall/goods/can-buy-by-school"
      body : "*"
    };
    option (openapi.v3.operation) = {
      summary: "通过用户所在学校是否允许购买该商品",
    };
  };
}
message IsRepeatPayGoodIdReq {
  // 商品id
  string goodsId = 1;
  // 用户id
  string userId = 2;
}
message IsRepeatPayGoodIdReply {
  bool isRepeat = 1;
}
message GetCombinationGoodsInfoReq {
  string goodsId = 1;
}

message GetGoodListReq{
  string userId = 1;
  bool isNormalGoods = 2;
}

message GetGoodListReply{
  repeated goodsItem rows = 1;
}

message goodsItem {
  message extra {
    google.protobuf.Any date = 1;
    google.protobuf.Any defaultDate = 2;
  }
  message p {
    string id = 1;
    int64 addTime = 2;
    string stage = 3;
    string subject = 4;
  }
  message dis {
    string id = 1;
    p params = 2;
  }
  message sku {
    bool isEnabled = 1;
    bool isStockEnabled = 2;
    repeated google.protobuf.Any images = 3;
    string mongoId = 4[json_name = "_id"];
    string name = 5;
    double amount = 6;
    dis distributor = 7;
    string updatedBy = 8;
    string createdBy = 9;
    string serialNumber = 10;
    google.protobuf.Timestamp createdAt = 11;
    google.protobuf.Timestamp updatedAt = 12;
    string affirmation = 13;
    string courseType = 14;
    int64 v = 15;
    string id = 16;
  }
  message skuItem {
    string skuId = 1;
    double amount = 2;
    extra extraParams = 3;
    sku sku = 4;
    float pointsAmount = 5;
  }
  double stopLossAmount = 1;
  bool buyOnce = 2;
  string status = 3;
  repeated string groupList = 4;
  repeated google.protobuf.Any images = 5;
  bool isEnabled = 6;
  bool isDeleted = 7;
  string mongoId = 8 [json_name = "_id"];
  string name = 9;
  string description = 10;
  double originalAmount = 11;
  string createdBy = 12;
  string updatedBy = 13;
  string  link = 14;
  google.protobuf.Timestamp createdAt = 15;
  google.protobuf.Timestamp updatedAt = 16;
  int32 skuSize = 17;
  double amount = 18;
  double pointsAmount = 19;
  string id = 20;
  int64 duration = 21;
  double unitPriceOneDay = 22;
  bool isBulk = 23;
  string subjectId = 24;
  string stageId = 25;
  repeated skuItem skuList = 26;
}

message CreateGoodsBySkuGroupReq{
  string id = 1;
  string name = 2;
  CreateGoodsChooseSkuList chooseSkuList =3;
}

message CreateGoodsChooseSku{
  string skuId = 1;
  double amount = 2;
}

message CreateGoodsChooseSkuList{
  repeated CreateGoodsChooseSku vip = 1;
  repeated CreateGoodsChooseSku specialCourse = 2;
  repeated CreateGoodsChooseSku derivativeProduct = 3;
  repeated CreateGoodsChooseSku pad = 4;
  repeated CreateGoodsChooseSku padFixedPriceSpecialCourse = 5;
  repeated CreateGoodsChooseSku phoneFixedPriceSpecialCourse = 6;
}

message GetSkuGroupGoodsListReq{
  // 非必填，商品分组，多个用,分隔.
  repeated string groups = 1;
  // 非必填，sku组商品id，多个用,分隔.
  repeated string ids = 2;
}

message SkuListItem {
  string skuId = 1;
  double amount = 2;
  Sku sku = 3;
}
message GetSkuGroupGoodsListReply{
  repeated SkuGroup rows = 1;
}
message SkuGroup {
  string id = 1;
  message ChooseSkuList {
    message VIP {
      repeated SkuListItem skuList = 1;
      double durationTarget = 2;
      double stageSubjectTarget = 3;
    }
    VIP vip = 1;
    message SpecialCourse {
      message SkuList {
        string skuId = 1;
        double amount = 2;
        string area = 3;
        message StartTime {}
        StartTime startTime = 4;
        message EndTime {}
        EndTime endTime = 5;
        repeated string textbook = 6;
        Sku sku = 7;
      }

      repeated SkuList skuList = 1;
      double durationTarget = 2;
      double areaTarget = 3;
    }
    SpecialCourse specialCourse = 2;
    message DerivativeProduct {
      repeated SkuListItem skuList = 1;
      double nameTarget = 2;
    }

    DerivativeProduct derivativeProduct = 3;
    message PAD {
      repeated SkuListItem skuList = 1;
      double nameTarget = 2;
    }
    PAD pad = 4;
    message PadFixedPriceSpecialCourse {
      message SkuList {
        string skuId = 1;
        double amount = 2;
        string area = 3;
        repeated SkuListItem textbook = 4;
        Sku sku = 5;
      }

      repeated SkuList skuList = 1;
      double durationTarget = 2;
      double areaTarget = 3;
    }

    PadFixedPriceSpecialCourse padFixedPriceSpecialCourse = 5;
    message PhoneFixedPriceSpecialCourse {
      message SkuList {
        string skuId = 1;
        double amount = 2;
        string area = 3;
        repeated SkuListItem textbook = 4;
        Sku sku = 5;
      }
      repeated SkuList skuList = 1;
      double durationTarget = 2;
      double areaTarget = 3;
    }
    PhoneFixedPriceSpecialCourse phoneFixedPriceSpecialCourse = 6;
  }
  ChooseSkuList chooseSkuList = 2;
  message AmountRange {
    double min = 1;
    double max = 2;
  }
  AmountRange amountRange = 3;
  repeated string paymentPlatform = 4;
  message BuyLimit {}
  BuyLimit buyLimit = 5;
  string status = 6;
  repeated string groupList = 7;
  double stopLossPercentage = 8;
  repeated string images = 9;
  repeated string orderImages = 10;
  bool isDeleted = 11;
  repeated string skuIdList = 12;
  repeated string skuKindList = 13;
  string name = 14;
  string description = 15;
  // message SellingTime {
  //   string begin = 1;
  // }
  // SellingTime sellingTime = 16;
  string note = 17;
  message RequiredSkuList {
    repeated SkuListItem textbook = 1;
    string skuId = 2;
    double amount = 3;
    string name = 4;
    Sku sku = 5;
  }
  repeated RequiredSkuList requiredSkuList = 18;
  double requiredSkuAmount = 19;
  string link = 20;
  string createdBy = 21;
  string updatedBy = 22;
  bool isFixedPrice = 23;
  string createdAt = 24;
  string updatedAt = 25;
  string mongoId = 26 [json_name = "_id"];
}

message GetGoodsInfoReq{
  string id = 1;
  string userId = 2;
  string regionCode = 3;
  string to = 4;
}

//message SkuAmount{
//  string name = 1;
//  float amount = 2;
//  float originalAmount = 3;
//  float discountedPrices = 4;
//  bool isBulk = 5;
//  string id = 6;
//}

message GetGoodsInfoReply{
  string goodsId = 1;
  string name = 2;
  string sellingPoint = 3;
  string img = 4;
  string video = 5;
  string desc = 6;
  int32 type = 7;
  int64 buyerCounter = 8;
  string videoCover = 9;
  bool isBulk = 10;
  int64 bulkExpired = 11;
  float amount = 12;
  float originalAmount = 13;
  repeated google.protobuf.Struct skus = 14;
  float discountedPrices = 15;
  repeated string attributes = 16;
  string goodsNamePrefix = 17;
}

message GetGroupGoodsListReq{}

message GetGroupGoodsListReply{
  // 团购过期时间.
  google.protobuf.Timestamp expireDate = 1;
  repeated GoodItem records = 2;
}

message Params{
  int64 addTime = 1;
  string stage = 2;
  string subject = 3;
  string kind = 4;
  string link = 5;
  string publisher = 6;
  string semester = 7;
  string examKind = 8;
  string Id = 9;
  Range range = 10;
  string name = 11;
  string contentKind = 12;
  message CONTENT {
    string stage = 1;
    string subject = 2;
    string semester = 3;
    string publisher = 4;
    string id = 5;
  }
  CONTENT content = 13;
}

message Range {
  int64 minTime = 1;
  int64 maxTime = 2;
}

message Distributor{
  string kind = 1;
  Params params = 2;
}
message Sku{
  string ID = 1 [json_name = "id"];
  string name = 2;
  Distributor distributor = 3;
  string serialNumber = 5;
  bool isEnabled = 6;
  repeated string images = 7;
  string createdAt = 8;
  string updatedAt = 9;
}

message SkuItem  {
  Sku sku = 1;
  string skuId = 2;
  // 到期型商品扩展参数.
  ExtraParams extraParams = 3;
}

message ExtraParams {
  // 开始时间.
  string startTime = 1;
  // 结束时间.
  string endTime = 2;
}

message AgentPrice{
  float discountedPrices = 1;
  float settlementPrices = 2;
}

message GoodItem  {
  string ID = 1 [json_name = "id"];
  string name = 2;
  bool buyOnce = 3;
  bool isEnabled = 4;
  bool isDeleted = 5;
  string status = 6;
  uint32 skuSize = 7;
  float amount = 8;
  string ID1 = 9 [json_name = "_id"];
  string link = 10;
  repeated string groupList = 11;
  repeated SkuItem skuList = 12;
  repeated string images = 13;
  AgentPrice agent = 14;
  repeated string paymentPlatform = 15;
  // 地区信息，仅包含专项课的商品可能含有该字段.
  repeated Region regions = 16;
  float groupBuyingSelfAmount = 17;
  string skuGroupGoodId = 18;
  // 团购商品前缀.
  string goodsNamePrefix = 19;
  // 团购商品工单ID.
  string entryId = 20;
  bool isBulk = 21;
}

message stageSubject {
  int32 stageId = 1;
  int32 subjectId = 2;
  string name = 3;
  string link = 4;
}
message GetStageSubjectsRequest {}
message GetStageSubjectsReply {
  repeated stageSubject records = 1;
}

// 资源.
message GoodsViewResource {
  // 资源类型：image、video、goods、gif.
  string type = 1;
  // 资源目标，类型为 image、gif 时该字段为图片地址，类型为 video 时该字段为视频地址，类型为 goods 时字段为空.
  string target = 2;
  // 扩展信息，类型为 image、gif 时该字段为空，类型为 video 时该字段为视频封面图，类型为 goods 时字段为空.
  string extra = 3;
}

// 请求参数：goodsGroup、stageId、subjectId、goodsId、viewId
message GetGoodsViewReq{
  // 必填，视图 id （即为页面资源配置id）.
  string viewId = 1;
  // 非必填，商品分组，多个用,分隔.
  string goodsGroup = 2;
  // 非必填，学段.
  int32 stageId = 3;
  // 非必填，学科.
  int32 subjectId = 4;
  // 非必填，商品id，多个用,分隔.
  string goodsId = 5;
  // 视图配置类型
  string viewType = 6;
}

message GetGoodsViewReply{
  // 商品列表.
  repeated CombinationGoods goodsList = 1;
  // 权益图片地址.
  string rightsImage = 2;
  // 浮动视频地址.
  string floatingVideo = 3;
  // 页面资源列表.
  repeated GoodsViewResource resourceList = 4;
}

message CombinationGoods {
  // 商品ID
  string id = 1;
  // 创建时间
  string createdAt = 2;
  // 是否删除
  bool isDeleted = 6;
  // 商品状态
  string status = 7;
  // 商品类型
  string goodKind = 8;
  // 商品名称
  string name = 9;
  // 购买限制
  int32 buyLimit = 10;
  // 备注
  string note = 11;
  // 支付平台列表
  repeated string paymentPlatform = 12;
  // 商品分组列表
  repeated string groupList = 13;
  // 商品价格(分)
  int64 amount = 14;
  // 商品原价(分)
  int64 originalAmount = 15;
  // 价格区间
  message AmountRange {
    int64 min = 1;
    int64 max = 2;
  }
  AmountRange amountRange = 16;
  // 止损比例
  float stopLossPercentage = 17;
  // 是否支持iOS支付
  bool canIosPay = 18;
  // iOS商品ID
  string iosProductId = 19;
  // 是否固定价格
  bool isFixedPrice = 20;
  // 年限
  string year = 21;
  // 商品内容
  string content = 22;
  // 商品业务类型
  string goodBizType = 23;
  // SKU ID列表
  repeated string skuIdList = 24;
  // SKU分组ID列表
  repeated string skuGroupIdList = 25;
  // SKU详情列表
  repeated Sku skuDetailList = 26;
  repeated Region regions =27;
}

message Region {
  // 区域名.
  string name = 1;
  // 区域码.
  string code = 2;
}

message goodsDetailByIDReq {
  string goodsId = 1;
}
message goodsDetailByIDReply {
  GoodItem data = 1;
}

message goodsMergeCouponReq {
  // 商品id.
  string goodsId = 1;
  // 合并后的商品名称，参数为空时取商品id对应的商品名称.
  string name = 2;
}

message GetTeacherGoodsListReq {}

message  TeacherGoodInfo {
  string id = 1;
  string oldId = 2 [json_name = "_id"];
  string description = 3;
  string name = 4;
  float amount = 5;
  float originalAmount = 6;
  string stageId = 7;
  string subjectId = 8;
  int32 topicTotal = 9;
}

message CheckCanBuyGoodsBySchoolReq {
  // 用户id
  string userId = 1;
  // 商品快照id
  string goodId  = 2;
  // 组合商品id
  string skuGroupGoodId = 3;
  // 二级商品类目ID
  string goodKindIdLevel2 = 4;
}

message CheckCanBuyGoodsBySchoolRes {
  // 是否可以购买, true为可以购买，false为不可以购买
  bool canBuy = 1;
}