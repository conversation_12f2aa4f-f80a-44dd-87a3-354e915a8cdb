//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	adminCouponBiz "teacher-mall/internal/admin/coupon/biz"
	adminCouponData "teacher-mall/internal/admin/coupon/data"
	adminCouponSrv "teacher-mall/internal/admin/coupon/service"
	adminGoodCourseBiz "teacher-mall/internal/admin/good_course/biz"
	adminGoodCourseData "teacher-mall/internal/admin/good_course/data"
	adminGoodCourseSrv "teacher-mall/internal/admin/good_course/service"
	adminGoodsBiz "teacher-mall/internal/admin/goods/biz"
	adminGoodsData "teacher-mall/internal/admin/goods/data"
	adminGoodsSrv "teacher-mall/internal/admin/goods/service"
	adminBiz "teacher-mall/internal/admin/shelf/biz"
	adminData "teacher-mall/internal/admin/shelf/data"
	adminSrv "teacher-mall/internal/admin/shelf/service"
	goodCourseLinkBiz "teacher-mall/internal/admin/shop_course_link/biz"
	goodCourseLinkData "teacher-mall/internal/admin/shop_course_link/data"
	goodCourseLinkService "teacher-mall/internal/admin/shop_course_link/service"
	"teacher-mall/internal/conf"
	"teacher-mall/internal/mall/biz"
	mallCouponBiz "teacher-mall/internal/mall/coupon/biz"
	mallCouponData "teacher-mall/internal/mall/coupon/data"
	mallCouponSrv "teacher-mall/internal/mall/coupon/service"
	goodCourseBiz "teacher-mall/internal/mall/goodcourse/biz"
	goodCourseData "teacher-mall/internal/mall/goodcourse/data"
	goodCourseSrv "teacher-mall/internal/mall/goodcourse/service"
	mallGoodsBiz "teacher-mall/internal/mall/goods/biz"
	mallGoodsData "teacher-mall/internal/mall/goods/data"
	mallGoodsSrv "teacher-mall/internal/mall/goods/service"
	orderBiz "teacher-mall/internal/mall/order/biz"
	orderData "teacher-mall/internal/mall/order/data"
	orderSrv "teacher-mall/internal/mall/order/service"
	mallScoresBiz "teacher-mall/internal/mall/scores/biz"
	mallScoresService "teacher-mall/internal/mall/scores/service"
	mallBiz "teacher-mall/internal/mall/shelf/biz"
	mallData "teacher-mall/internal/mall/shelf/data"
	mallSrv "teacher-mall/internal/mall/shelf/service"
	"teacher-mall/internal/pkg/db"
	"teacher-mall/internal/pkg/middleware"
	scoreBiz "teacher-mall/internal/score/biz"
	scoreData "teacher-mall/internal/score/data"
	scoreService "teacher-mall/internal/score/service"
	"teacher-mall/internal/server"
	thirdparty "teacher-mall/third_party"
	basicResourceThird "teacher-mall/third_party/basicresource"
	bronnThird "teacher-mall/third_party/bronn"
	"teacher-mall/third_party/channel"
	"teacher-mall/third_party/crs"
	"teacher-mall/third_party/discount"
	goodThird "teacher-mall/third_party/good"
	"teacher-mall/third_party/good_order"
	orderThird "teacher-mall/third_party/order"
	"teacher-mall/third_party/order_plus"
	"teacher-mall/third_party/qiniu"
	"teacher-mall/third_party/revenue_strategy"
	"teacher-mall/third_party/school"
	"teacher-mall/third_party/shortlink"
	"teacher-mall/third_party/teacher"
	"teacher-mall/third_party/teacheruser"
	"teacher-mall/third_party/teachingresource"
	"teacher-mall/third_party/usercore"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

// wireApp init kratos application.
func wireApp(*conf.Bootstrap, *conf.Server, *conf.Data, log.Logger) (*kratos.App, func(), error) {
	panic(wire.Build(
		revenue_strategy.ProviderSet,
		thirdparty.ProviderSet,
		bronnThird.ProviderSet,
		shortlink.ProviderSet,
		server.ProviderSet,
		order_plus.ProviderSet,
		orderThird.ProviderSet,
		goodThird.ProviderSet,
		discount.ProviderSet,
		adminCouponSrv.ProviderSet,
		adminCouponBiz.ProviderSet,
		adminCouponData.ProviderSet,
		mallCouponSrv.ProviderSet,
		mallCouponBiz.ProviderSet,
		mallCouponData.ProviderSet,
		teachingresource.ProviderSet,
		usercore.ProviderSet,
		teacher.ProviderSet,
		channel.ProviderSet,
		mallScoresBiz.ProviderSet,
		mallScoresService.ProviderSet,
		adminSrv.ProviderSet,
		adminData.ProviderSet,
		adminBiz.ProviderSet,
		adminGoodsData.ProviderSet,
		adminGoodsBiz.ProviderSet,
		adminGoodsSrv.ProviderSet,
		middleware.ProviderSet,
		db.ProviderSet,
		mallSrv.ProviderSet,
		mallData.ProviderSet,
		mallBiz.ProviderSet,
		mallGoodsSrv.ProviderSet,
		mallGoodsData.ProviderSet,
		mallGoodsBiz.ProviderSet,
		adminGoodCourseSrv.ProviderSet,
		adminGoodCourseData.ProviderSet,
		adminGoodCourseBiz.ProviderSet,
		orderBiz.ProviderSet,
		orderSrv.ProviderSet,
		biz.ProviderSet,
		qiniu.ProviderSet,
		basicResourceThird.ProviderSet,
		goodCourseSrv.ProviderSet,
		goodCourseBiz.ProviderSet,
		goodCourseData.ProviderSet,
		crs.ProviderSet,
		teacheruser.ProviderSet,
		orderData.ProviderSet,
		scoreBiz.ProviderSet,
		scoreService.ProviderSet,
		scoreData.ProviderSet,
		good_order.ProviderSet,
		goodCourseLinkBiz.ProviderSet,
		goodCourseLinkData.ProviderSet,
		goodCourseLinkService.ProviderSet,
		school.ProviderSet,
		newNacos,
		newApp,
	))
}
