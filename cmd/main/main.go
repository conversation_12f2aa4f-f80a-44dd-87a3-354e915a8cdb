package main

import (
	"flag"
	"os"
	"teacher-mall/common/enum"
	"teacher-mall/internal/conf"

	"github.com/go-kratos/kratos/contrib/registry/nacos/v2"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/env"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/nacos-group/nacos-sdk-go/clients"
	"github.com/nacos-group/nacos-sdk-go/common/constant"
	"github.com/nacos-group/nacos-sdk-go/vo"
	"gitlab.yc345.tv/backend/utils/v2/observer"
	tracingcommon "gitlab.yc345.tv/security-and-payment/tracing/common"
	"gopkg.in/natefinch/lumberjack.v2"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// flagconf is the config flag.
	flagconf             string
	goodsPageDirPathConf string
	id, _                = os.Hostname()
)

func init() {
	switch os.Getenv("GO_ENV") {
	case "local":
		goodsPageDirPathConf = "../../configs/"
		flagconf = "../../configs/local.yaml"
	case "development":
		goodsPageDirPathConf = "./configs/"
		flagconf = "./configs/development.yaml"
	case "stage":
		goodsPageDirPathConf = "./configs/"
		flagconf = "./configs/stage.yaml"
	case "master":
		goodsPageDirPathConf = "./configs/"
		flagconf = "./configs/master.yaml"
	default:
		goodsPageDirPathConf = "./configs/"
		flagconf = "./configs/master.yaml"
	}
}

func newNacos(c *conf.Data) *nacos.Registry {
	sc := []constant.ServerConfig{
		*constant.NewServerConfig(c.Nacos.Host, c.Nacos.Port),
	}
	cc := constant.ClientConfig{
		Username:            c.Nacos.Username,
		Password:            c.Nacos.Password,
		NotLoadCacheAtStart: true,
		LogLevel:            `error`,
		LogRollingConfig: &lumberjack.Logger{
			MaxSize: 10, // MaxSize is the maximum size in megabytes of the log file before it gets rotated. It defaults to 100 megabytes.
		},
	}
	client, err := clients.NewNamingClient(
		vo.NacosClientParam{
			ServerConfigs: sc,
			ClientConfig:  &cc,
		},
	)
	if err != nil {
		panic(err)
	}
	return nacos.New(client)
}

func newApp(nacos *nacos.Registry, logger log.Logger, hs *http.Server, gs *grpc.Server) *kratos.App {
	option := []kratos.Option{
		kratos.ID(enum.ID),
		kratos.Name(enum.NAME),
		kratos.Version(enum.VERSION),
		kratos.Metadata(map[string]string{}),
		kratos.Logger(logger),
		kratos.Server(
			hs,
			gs,
			observer.NewServer(),
		),
	}
	if os.Getenv("GO_ENV") != "local" || os.Getenv("GOENV") != "" {
		option = append(option, kratos.Registrar(nacos))
	}
	return kratos.New(option...)
}

func main() {
	flag.Parse()
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", enum.ID,
		"service.name", enum.NAME,
		"service.version", enum.VERSION,
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
	)
	c := config.New(
		config.WithSource(
			env.NewSource(""),
			file.NewSource(flagconf),
		),
	)
	defer c.Close()
	if err := c.Load(); err != nil {
		panic(err)
	}
	var bc conf.Bootstrap
	if err := c.Scan(&bc); err != nil {
		panic(err)
	}
	// 初始化链路追踪
	if err := tracingcommon.Init(tracingcommon.SetupConfig{
		ServiceName: enum.NAME + ".teacherschool",
		Version:     enum.VERSION,
		Ratio:       1, // 采样率
	}); err != nil {
		panic(err)
	}
	defer tracingcommon.Shotdown()
	bc.Data.GoodsPageDataDir = goodsPageDirPathConf
	app, cleanup, err := wireApp(bc, bc.Server, bc.Data, logger)
	if err != nil {
		panic(err)
	}
	defer cleanup()
	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}
}
