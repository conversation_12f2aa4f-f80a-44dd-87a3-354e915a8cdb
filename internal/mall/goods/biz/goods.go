package biz

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"sort"
	"strconv"
	"strings"
	pb "teacher-mall/api/mall/goods"
	adminBiz "teacher-mall/internal/admin/goods/biz"
	"teacher-mall/internal/common"
	"teacher-mall/internal/common/enum"
	"teacher-mall/internal/conf"
	"teacher-mall/internal/mall/biz"
	"teacher-mall/internal/model"
	"teacher-mall/third_party/channel"
	"teacher-mall/third_party/crs"
	"teacher-mall/third_party/order"
	"teacher-mall/third_party/qiniu"
	"teacher-mall/third_party/school"
	"teacher-mall/third_party/teacher"
	"teacher-mall/third_party/teacheruser"
	"teacher-mall/third_party/teachingresource"
	"teacher-mall/third_party/usercore"
	"time"

	"github.com/spf13/cast"
	"google.golang.org/protobuf/types/known/structpb"

	httpErrors "github.com/go-kratos/kratos/v2/errors"
	"github.com/jinzhu/now"
	"github.com/pkg/errors"

	"teacher-mall/third_party/good"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
	skuGood "gitlab.yc345.tv/backend/good/api/sku_good"
	"golang.org/x/sync/errgroup"
)

type GoodsUseCase struct {
	qiniuDomain           string
	log                   *log.Helper
	orderThird            order.IOrderThird
	teachingResourceThird teachingresource.ITeachingResourceThird
	teacherThird          teacher.ITeacherThird
	channelThird          channel.IChannelThird
	viewDataRepo          ViewDataRepo
	stageSubjectRepo      StageSubjectRepo
	goodsRepo             GoodsRepo
	qiniuThird            qiniu.IQiniuThird
	commonUseCase         *biz.CommonUseCase
	teacherUserThird      teacheruser.ITeacherUserThird
	crsThird              crs.ICrs
	goodThird             good.IGoodThird
	agencyPaymentRepo     adminBiz.IAgencyPaymentRepo
	userCoreThird         usercore.IUserCoreThird
	bootstrapConfig       *conf.Bootstrap
	schoolThird           school.ISchoolThird
}

func NewGoodsUseCase(
	c *conf.Data,
	logger log.Logger,
	orderThird order.IOrderThird,
	teachingResourceThird teachingresource.ITeachingResourceThird,
	teacherThird teacher.ITeacherThird,
	channelThird channel.IChannelThird,
	viewDataRepo ViewDataRepo,
	stageSubjectRepo StageSubjectRepo,
	goodsRepo GoodsRepo,
	qiniuThird qiniu.IQiniuThird,
	commonUseCase *biz.CommonUseCase,
	teacherUser teacheruser.ITeacherUserThird,
	crsThird crs.ICrs,
	goodThird good.IGoodThird,
	agencyPaymentRepo adminBiz.IAgencyPaymentRepo,
	userCoreThird usercore.IUserCoreThird,
	bootstrapConfig *conf.Bootstrap,
	schoolThird school.ISchoolThird,
) *GoodsUseCase {
	return &GoodsUseCase{
		qiniuDomain:           c.Qiniu.Domain,
		log:                   log.NewHelper(logger),
		orderThird:            orderThird,
		teachingResourceThird: teachingResourceThird,
		teacherThird:          teacherThird,
		channelThird:          channelThird,
		viewDataRepo:          viewDataRepo,
		stageSubjectRepo:      stageSubjectRepo,
		goodsRepo:             goodsRepo,
		qiniuThird:            qiniuThird,
		commonUseCase:         commonUseCase,
		teacherUserThird:      teacherUser,
		crsThird:              crsThird,
		goodThird:             goodThird,
		agencyPaymentRepo:     agencyPaymentRepo,
		userCoreThird:         userCoreThird,
		bootstrapConfig:       bootstrapConfig,
		schoolThird:           schoolThird,
	}
}

type GoodsRepo interface {
	FindOne(ctx context.Context, goodsId string) (*model.Goods, error)
	CacheIncr(ctx context.Context, key string) error
	CacheGet(ctx context.Context, key string) (string, error)
	CacheSet(ctx context.Context, key string, value interface{}, expiration *time.Duration) error
	GetSavingsCardGoodsID() string
}

type ViewDataRepo interface {
	FindById(ctx context.Context, id string) (*model.ViewData, error)
}

type StageSubjectRepo interface {
	FindAll(ctx context.Context) ([]model.StageSubject, error)
}

func (uc *GoodsUseCase) IsRepeatPayGoodId(ctx context.Context, req *pb.IsRepeatPayGoodIdReq) (bool, error) {
	agencyPaymentGoods, err := uc.agencyPaymentRepo.FindAgencyGoodsByUserID(ctx, req.UserId)
	if err != nil {
		return false, err
	}
	detail := adminBiz.AgencyPaymentGoodDetail{}

	for i := 0; i < len(agencyPaymentGoods); i++ {
		err = agencyPaymentGoods[i].GoodsInfo.AssignTo(&detail)
		if err != nil {
			uc.log.Errorf("GetRecentAgencyGoods FindRecentAgencyGoods error:%s", err.Error())
		}
		if detail.ID == req.GoodsId {
			return true, nil
		}
	}
	return false, nil
}

func (uc *GoodsUseCase) GetCombinationGoodsDetailByID(ctx context.Context, userID, goodsID string) (*structpb.Struct, error) {
	res := &structpb.Struct{}
	groupBuying, err := uc.GetGroupBuyingInfo(ctx, userID)
	if err != nil {
		return nil, err
	}

	res, err = uc.goodThird.GetCombinationGoodsInfo(ctx, goodsID)
	if err != nil {
		uc.log.Errorf("get goods by id error: %v", err)
		return nil, httpErrors.BadRequest("获取商品失败", "获取商品失败")
	}
	res.Fields["isBulk"] = structpb.NewBoolValue(false)
	res.Fields["entryId"] = structpb.NewStringValue("")
	if groupBuying.Goods != nil {
		g, exist := lo.Find(*groupBuying.Goods, func(item channel.GroupBuyingGoods) bool {
			return item.Goods.ID == goodsID
		})
		res.Fields["isBulk"] = structpb.NewBoolValue(exist)
		if exist {
			res.Fields["entryId"] = structpb.NewStringValue(g.EntryId)
		}
	}
	return res, nil
}
func (uc *GoodsUseCase) PageViewIncr(ctx context.Context, goodsId string) error {
	return uc.goodsRepo.CacheIncr(ctx, fmt.Sprintf("#goods-pageviews-%s", goodsId))
}

func (uc *GoodsUseCase) GetStageSubjectList(ctx context.Context) ([]StageSubject, error) {
	var res []StageSubject
	ss, err := uc.stageSubjectRepo.FindAll(ctx)
	if err != nil {
		return res, err
	}
	err = copier.Copy(&res, ss)
	return res, errors.WithStack(err)
}

func (uc *GoodsUseCase) GetGoodsView(ctx context.Context, userId, viewId string, groups, goodsId []string, stageId, subjectId int32) (*GoodsView, error) {
	// GoodsList   RightsImage    ResourceList
	var res GoodsView
	status := []string{"已上架"}
	// thirdGoodsList, _, err := uc.orderThird.GoodsList(ctx, "0", "all", groups, status, goodsId, nil, nil)
	thirdGoodsList, err := uc.goodThird.GetAllCombinationGoods(ctx, good.GetCombinationGoodsListReq{
		GoodIds:    goodsId,
		GroupList:  groups,
		StatusList: status,
		Limit:      100,
		Offset:     0,
	})
	if err != nil {
		return &res, err
	}
	//needCheckAddTime := lo.ContainsBy(groups, func(g string) bool {
	//	return g == "schoolStudentApp" || g == "schoolMultiSubject"
	//})
	//if len(userId) > 0 && needCheckAddTime {
	//	// 通过用户访问，需要根据是否为重点维护校和普通维护校过滤商品
	//	isDeepOperationSchool, isSchoolRoom, err := uc.getMaintainSchoolInfo(ctx, userId)
	//	if err != nil {
	//		return nil, err
	//	}
	//	if isDeepOperationSchool && isSchoolRoom {
	//		thirdGoodsList = lo.Filter(thirdGoodsList, func(g *good.CombinationGoods, _ int) bool {
	//			var addTime int64
	//			for i := 0; i < len(g.SkuDetailList); i++ {
	//				skuItem := g.SkuDetailList[i]
	//				if skuItem == nil || skuItem.Distributor == nil || skuItem.Distributor.Params == nil {
	//					continue
	//				}
	//				param := skuItem.Distributor.Params
	//				if len(param.Subject) > 0 && len(param.Stage) > 0 {
	//					addTime = param.AddTime
	//					break
	//				}
	//			}
	//			yearTime := int64(1000 * 60 * 60 * 24 * 31 * 12)
	//			return addTime >= yearTime
	//		})
	//	}
	//}
	var goodsList []*CombinationGoods
	err = copier.Copy(&goodsList, thirdGoodsList)
	if err != nil {
		return &res, errors.WithStack(err)
	}
	goodsList = lo.Filter(goodsList, func(g *CombinationGoods, _ int) bool {
		ok := uc.checkSkuList(g.SkuDetailList, stageId, subjectId)
		return ok
	})
	// 判断商品中是否有专项课如果有专项课填充专项课学科学段（将专项课的 sku 拆分为当前商品的多个 sku）
	err = uc.fillSpecialStageSubjectToGoods(ctx, goodsList)
	if err != nil {
		return &res, err
	}
	// 添加配置页面信息信息
	viewData, err := uc.viewDataRepo.FindById(ctx, viewId)
	if err != nil {
		return &res, err
	}
	if viewData != nil {
		err = copier.Copy(&res, viewData)
		if err != nil {
			return &res, errors.WithStack(err)
		}
	}
	res.GoodsList = lo.Filter(goodsList, func(g *CombinationGoods, _ int) bool {
		// 过滤任意一个非实物sku学科学段能够匹配的
		return uc.checkStageSubject(g, stageId, subjectId)
	})
	return &res, nil
}

func (uc *GoodsUseCase) checkStageSubject(g *CombinationGoods, stageId, subjectId int32) bool {
	if stageId == 0 && subjectId == 0 {
		return true
	}
	for i := 0; i < len(g.SkuDetailList); i++ {
		s := g.SkuDetailList[i]
		if s == nil {
			continue
		}
		if (s.Distributor.Params.Stage == "" && stageId > 0) || (s.Distributor.Params.Subject == "" && subjectId > 0) {
			continue
		}
		if s.Distributor.Params.Stage != fmt.Sprintf("%d", stageId) {
			continue
		}
		if s.Distributor.Params.Subject != fmt.Sprintf("%d", subjectId) {
			continue
		}
		return true
	}
	return false
}

// 检查商品下的 sku 列表是否包含学科学段（只剩下包含 kind 为 vip、timingVip、specialCourse 的 sku ，且其中任意一个 sku 学科学段匹配的列表）
func (uc *GoodsUseCase) checkSkuList(skuList []*Sku, stageId, subjectId int32) bool {
	var res []*Sku
	for i := 0; i < len(skuList); i++ {
		s := skuList[i]
		if s == nil {
			continue
		}
		dis := s.Distributor
		p := dis.Params
		if dis.Kind == enum.SKU_KIND_SPECIAL {
			// 专项课
			if len(p.Id) == 0 {
				continue
			}
			res = append(res, s)
		} else if dis.Kind == enum.SKU_KIND_VIP || dis.Kind == enum.SKU_KIND_TIMING_VIP {
			if (p.Stage == fmt.Sprintf("%d", stageId) || stageId == 0) && (p.Subject == fmt.Sprintf("%d", subjectId) || subjectId == 0) {
				res = append(res, s)
			}
		}
	}
	return len(res) > 0
}

func (uc *GoodsUseCase) fillSpecialStageSubjectToGoods(ctx context.Context, goodsList []*CombinationGoods) error {
	pkgList, err := uc.teachingResourceThird.GetSpecialCoursePkgListByType(ctx, "all")
	if err != nil {
		return err
	}
	coursePkgMap := map[string]*teachingresource.SpecialCoursePackage{}
	lo.ForEach(pkgList, func(pkg teachingresource.SpecialCoursePackage, idx int) {
		coursePkgMap[pkg.Id] = &pkg
	})
	lo.ForEach(goodsList, func(g *CombinationGoods, _ int) {
		skuList := g.SkuDetailList
		g.SkuDetailList = make([]*Sku, 0)
		regions := make([]Region, 0)
		for i := 0; i < len(skuList); i++ {
			s := skuList[i]
			if s == nil {
				continue
			}
			if s.Distributor.Kind == enum.SKU_KIND_SPECIAL {
				coursePkg := coursePkgMap[s.Distributor.Params.Id]
				if coursePkg == nil {
					continue
				}
				if len(regions) == 0 && len(coursePkg.Regions) > 0 {
					// 多个专项课sku情况下，以第一个专项课sku的地区
					r := coursePkg.Regions[0]
					regions = append(regions, Region{
						Name: r.Name,
						Code: r.Code,
					})
				}
				newSkuList, err := uc.generateSkuList(s, coursePkg.StageIds, coursePkg.SubjectIds)
				if err != nil {
					return
				}
				g.SkuDetailList = append(g.SkuDetailList, newSkuList...)
			} else {
				g.SkuDetailList = append(g.SkuDetailList, s)
			}
		}
		g.Regions = regions
	})
	return nil
}

func (uc *GoodsUseCase) generateSkuList(baseSku *Sku, stageIds, subjectIds []int) ([]*Sku, error) {
	var res []*Sku
	for _, stageId := range stageIds {
		for _, subjectId := range subjectIds {
			s := Sku{}
			err := copier.CopyWithOption(&s, baseSku, copier.Option{
				DeepCopy: true,
			})
			if err != nil {
				return res, errors.WithStack(err)
			}
			s.Distributor.Params.Stage = fmt.Sprintf("%d", stageId)
			s.Distributor.Params.Subject = fmt.Sprintf("%d", subjectId)
			res = append(res, &s)
		}
	}
	return res, nil
}

func (uc *GoodsUseCase) GetGroupGoodsList(ctx context.Context, userId string) ([]*GroupBuyingGoods, *time.Time, error) {
	var res []*GroupBuyingGoods
	members, err := uc.teacherThird.GetUserMembersWithRoom(ctx, []string{userId})
	if err != nil {
		return res, nil, err
	}
	var roomIds []string
	roomIds = lo.Map(members, func(m *teacher.MembersWithRoom, _ int) string {
		return m.AdminRoom.Id
	})
	roomIds = append(roomIds, lo.Map(members, func(m *teacher.MembersWithRoom, _ int) string {
		return m.AdminRoom.ID_
	})...)
	goodsList, expireDate, err := uc.getRoomsGroupGoods(ctx, roomIds)
	if err != nil {
		return res, expireDate, err
	}
	err = copier.Copy(&res, goodsList)
	return res, expireDate, err
}

func (uc *GoodsUseCase) GetGoodsInfo(ctx context.Context, goodsId, userId, regionCode, to string) (GoodsInfo, error) {
	var res GoodsInfo
	var err error
	goodsExtInfo, err := uc.goodsRepo.FindOne(ctx, goodsId)
	if err != nil {
		return res, err
	}
	goodsDetail, err := uc.orderThird.GetGoodsDetail(ctx, goodsId)
	if err != nil {
		return res, err
	}
	goods := goodsDetail.GoodsById
	coursePack := goodsDetail.CoursePackById
	goodsName := goods.Name
	goodsType := goodsDetail.Type
	if len(goodsName) == 0 {
		goodsName = coursePack.Data.Name
	}
	video, err := uc.qiniuThird.GetAuthedResource(ctx, userId, goodsExtInfo.Video)
	if err != nil {
		return res, err
	}
	res = GoodsInfo{
		GoodsId:      goodsId,
		Name:         goodsName,
		SellingPoint: goodsExtInfo.SellingPoint,
		Img:          lo.If(len(goodsExtInfo.Img) > 0, fmt.Sprintf("%s/%s", uc.qiniuDomain, goodsExtInfo.Img)).Else(""),
		Video:        video,
		Desc:         goodsExtInfo.Desc,
		Type:         lo.If(goodsExtInfo.Type != 0, int32(goodsExtInfo.Type)).Else(goodsType),
		BuyerCounter: 99999,
		VideoCover:   goodsExtInfo.VideoCover,
		IsBulk:       false, // 是否是团购？   // 多组合商品团购信息以“内部商品的最低团购价商品的信息为准”；
		Skus:         make([]*SkuAmount, 0),
	}
	subjectId := ""
	var groupBuying biz.GroupBuying
	if len(userId) > 0 {
		groupBuying, err = uc.commonUseCase.GetUserGroupBuyingGoods(ctx, userId)
		if groupBuying.IsGroupBuying {
			// 负号
			diff := now.New(time.Now()).Sub(*groupBuying.ExpireDate)
			res.BulkExpired = int64(math.Round(math.Abs(diff.Seconds())))
		}
	}
	var groupBuyingGoodsInfo biz.GroupBuyingGoods
	var isGroupBuyingGoods bool
	if groupBuying.IsGroupBuying {
		groupBuyingGoodsInfo, isGroupBuyingGoods = lo.Find(*groupBuying.Goods, func(g biz.GroupBuyingGoods) bool {
			return g.ID == goodsId || g.ID_ == goodsId
		})
	}
	if isGroupBuyingGoods {
		res.GoodsNamePrefix = groupBuyingGoodsInfo.GoodsNamePrefix
	}
	if goodsType == enum.GOOD_SINGLE_SKU || goodsType == enum.GOOD_MULTITAL_SKU {
		if len(userId) > 0 && groupBuying.IsGroupBuying {
			if isGroupBuyingGoods {
				res.IsBulk = true
				if groupBuyingGoodsInfo.GroupBuyingSelfAmount > 0 {
					res.DiscountedPrices = groupBuyingGoodsInfo.GroupBuyingSelfAmount
				} else if groupBuyingGoodsInfo.Agent != nil && groupBuyingGoodsInfo.Agent.DiscountedPrices > 0 {
					res.DiscountedPrices = groupBuyingGoodsInfo.Agent.DiscountedPrices
				} else {
					res.DiscountedPrices = groupBuyingGoodsInfo.Amount
				}
			}
		}
		res.Amount = goods.Amount
		res.OriginalAmount = goods.OriginalAmount
		res.Skus = lo.Map(goods.SkuList, func(sku order.SkuList, _ int) *SkuAmount {
			return &SkuAmount{
				Name:      strings.ReplaceAll(strings.ReplaceAll(sku.Sku.Name, "虚拟-VIP-", ""), "虚拟-专项-", ""),
				Amount:    sku.Sku.Amount,
				AddTime:   sku.Sku.Distributor.Params.AddTime,
				Kind:      sku.Sku.Distributor.Kind,
				StageId:   sku.Sku.Distributor.Params.Stage,
				SubjectId: sku.Sku.Distributor.Params.Subject,
			}
		})
		if len(goods.SkuList) == 0 {
			subjectId = "nonSubject"
		} else {
			subjectId = lo.If(len(goods.SkuList[0].Sku.Distributor.Params.Subject) == 0, "nonSubject").Else(goods.SkuList[0].Sku.Distributor.Params.Subject)
		}
	} else {
		// 处理coursePack
		m := coursePack.FindMinPriceGoodsFromCoursePack()
		res.Amount = m.Amount
		res.OriginalAmount = m.OriginalAmount
		if len(m.SkuList) == 0 {
			subjectId = "nonSubject"
		} else if m.SkuList[0].Sku == nil {
			subjectId = "nonSubject"
		} else {
			subjectId = lo.If(len(m.SkuList[0].Sku.Distributor.Params.Subject) == 0, "nonSubject").Else(m.SkuList[0].Sku.Distributor.Params.Subject)
		}
		// 如果是多组合商品，必须要有科学id；
		if groupBuying.IsGroupBuying {
			var minPriceGroupBuyGoods *biz.GroupBuyingGoods
			bgGoodsList := lo.Filter(*groupBuying.Goods, func(g biz.GroupBuyingGoods, _ int) bool {
				_, ok := lo.Find(coursePack.Data.Goods, func(cg order.Goods) bool {
					return cg.ID == g.ID
				})
				return ok
			})
			if len(bgGoodsList) > 0 {
				minPriceGoods := bgGoodsList[0]
				for i := 1; i < len(bgGoodsList); i++ {
					if minPriceGoods.GroupBuyingSelfAmount > bgGoodsList[i].GroupBuyingSelfAmount {
						minPriceGoods = bgGoodsList[i]
					}
				}
				minPriceGroupBuyGoods = &minPriceGoods
			}
			if minPriceGroupBuyGoods != nil {
				res.IsBulk = true
				if minPriceGroupBuyGoods.GroupBuyingSelfAmount > 0 {
					res.DiscountedPrices = minPriceGroupBuyGoods.GroupBuyingSelfAmount
				} else if minPriceGroupBuyGoods.Agent != nil && minPriceGroupBuyGoods.Agent.DiscountedPrices > 0 {
					res.DiscountedPrices = minPriceGroupBuyGoods.Agent.DiscountedPrices
				} else {
					res.DiscountedPrices = minPriceGroupBuyGoods.Amount
				}
				res.Amount = minPriceGroupBuyGoods.Amount
				res.OriginalAmount = minPriceGroupBuyGoods.OriginalAmount
				if len(minPriceGroupBuyGoods.SkuList) == 0 {
					subjectId = "nonSubject"
				} else if minPriceGroupBuyGoods.SkuList[0].Sku != nil && minPriceGroupBuyGoods.SkuList[0].Sku.Distributor != nil && minPriceGroupBuyGoods.SkuList[0].Sku.Distributor.Params != nil && len(minPriceGroupBuyGoods.SkuList[0].Sku.Distributor.Params.Subject) > 0 {
					subjectId = minPriceGroupBuyGoods.SkuList[0].Sku.Distributor.Params.Subject
				} else {
					subjectId = "nonSubject"
				}
			}
		}
		res.Attributes = coursePack.Data.Attributes
		res.Skus = lo.Map(coursePack.Data.Goods, func(good order.Goods, _ int) *SkuAmount {
			discountedPrices := float32(0)
			if !isGroupBuyingGoods {
				discountedPrices = float32(0)
			} else if groupBuyingGoodsInfo.GroupBuyingSelfAmount > 0 {
				discountedPrices = groupBuyingGoodsInfo.GroupBuyingSelfAmount
			} else if groupBuyingGoodsInfo.Agent != nil && groupBuyingGoodsInfo.Agent.DiscountedPrices > 0 {
				discountedPrices = groupBuyingGoodsInfo.Agent.DiscountedPrices
			} else {
				discountedPrices = groupBuyingGoodsInfo.Amount
			}
			r := SkuAmount{
				Name:             good.Name,
				Amount:           good.Amount,
				OriginalAmount:   good.OriginalAmount,
				DiscountedPrices: discountedPrices,
				IsBulk:           isGroupBuyingGoods,
				Id:               good.ID1,
				Attributes:       make(map[string]string),
			}
			for _, kv := range good.Attributes {
				r.Attributes[kv.Name] = kv.Value
			}
			return &r
		})
	}
	counter, _ := uc.getBuyerCounter(ctx, regionCode, goodsId, subjectId)
	res.BuyerCounter = int64(math.Round(counter))
	return res, err
}

func (uc *GoodsUseCase) getRoomsGroupGoods(ctx context.Context, roomIds []string) (*[]GroupBuyingGoods, *time.Time, error) {
	res := make([]GroupBuyingGoods, 0)
	var expireDate *time.Time
	roomIds = lo.Filter(lo.Uniq(roomIds), func(id string, _ int) bool {
		return strings.TrimSpace(id) != ""
	})
	if len(roomIds) == 0 {
		return &res, expireDate, nil
	}
	groupBuying, err := uc.channelThird.GetRoomsGroupBuying(ctx, roomIds)
	if err != nil {
		return &res, expireDate, err
	}
	if !groupBuying.IsGroupBuying || len(*groupBuying.Goods) == 0 {
		return &res, expireDate, nil
	}
	goodsList := groupBuying.Goods
	err = copier.CopyWithOption(&res, *goodsList, copier.Option{
		IgnoreEmpty: true,
	})
	if err != nil {
		return &res, expireDate, err
	}
	expireDate = groupBuying.ExpireDate
	return &res, expireDate, err
}

// 通过学校区域代码查询学校所在城市的级别（城市级别是指一线城市、二线城市。。。）
func (uc *GoodsUseCase) findCityLevelBySchoolRegionCode(schoolReginCode string) (string, error) {
	// 区域代码长度是6
	if len(schoolReginCode) != 6 {
		return "", errors.New("schoolReginCode must be six character")
	}
	cityCodeLevel := common.GetCityCodeLevel()
	prefix := lo.Substring(schoolReginCode, 0, 3)
	cities := cityCodeLevel[prefix]
	cityCodes := make([]string, 0)
	for k, _ := range cities {
		cityCodes = append(cityCodes, k)
	}
	if len(cityCodes) == 0 {
		return "", errors.New(fmt.Sprintf("%s not in cityCodeLevel. please check schoolReginCode or cityCodeLevel.js", schoolReginCode))
	}
	if len(cityCodes) == 1 {
		return cities[cityCodes[0]], nil
	}
	matchedLengths := lo.Map(cityCodes, func(c string, _ int) int {
		counter := 0
		for i := 0; i < 6; i++ {
			if c[i] == schoolReginCode[i] {
				counter++
			} else {
				break
			}
		}
		return counter
	})
	maxMatchedIndex := 0
	for i := 1; i < len(matchedLengths); i++ {
		if matchedLengths[i] > matchedLengths[maxMatchedIndex] {
			maxMatchedIndex = i
		}
	}
	return cities[cityCodes[maxMatchedIndex]], nil
}

/**
 * 根据城市级别获取购买人数基数，基数一旦确定，就不能再变更
 * @param {*} cityLevel 城市级别
 * 一线城市基数范围 600 ～ 1200
 * 新一线和二线城市基数范围 800 ～ 1500
 * 三线城市基数范围 1500 ～ 3000
 * 四线城市基数范围 500 ~ 1500
 */
func (uc *GoodsUseCase) getBuyerCounterBase(ctx context.Context, cityLevel string) (float64, error) {
	key := fmt.Sprintf("#city-level-%s", cityLevel)
	cache, err := uc.goodsRepo.CacheGet(ctx, key)
	if err != nil {
		return 0, err
	}
	base, _ := strconv.Atoi(cache)
	if base > 0 {
		return float64(base), nil
	}
	rand.Seed(time.Now().UnixNano())
	if cityLevel == "first" {
		base = 600 + rand.Intn(600)
	} else if cityLevel == "newFirst" || cityLevel == "second" {
		base = 800 + rand.Intn(700)
	} else if cityLevel == "third" {
		base = 1500 + rand.Intn(1500)
	} else if cityLevel == "fifth" {
		base = 500 + rand.Intn(1000)
	}
	err = uc.goodsRepo.CacheSet(ctx, key, base, nil)
	return float64(base), err
}

/**
 * 获取本市该商品的购买数量
 * 背景：没有针对商品订单的数量做区域统计，就无法获取某商品在某城市的售卖数量。
 * 通过下面该方法生成一个的商品销售量。规则如下：
 * 计算公式：base * 系数 + 该商品访问量/50
 * base: 基数，不同城市有不同的基数，可参看getBuyerCounterBase方法的注释
 * 系数: 不同的学科系数不同。数学50%，物理30%，化学10%，其他10%
 * @param schoolReginCode 学校区域代码
 * @param goodsId 商品id
 * @param subjectId
 */
func (uc *GoodsUseCase) getBuyerCounter(ctx context.Context, schoolReginCode, goodsId, subjectId string) (float64, error) {
	buyerCounter := float64(9999)
	if len(schoolReginCode) == 0 {
		return buyerCounter, nil
	}
	// 1. 获取城市代码级别
	cityLev, err := uc.findCityLevelBySchoolRegionCode(schoolReginCode)
	if err != nil {
		return buyerCounter, err
	}
	// 2. 根据城市代码获取基数
	base, err := uc.getBuyerCounterBase(ctx, cityLev)
	if err != nil {
		return buyerCounter, err
	}
	// 3. 查询访问该商品的访问量
	amountStr, err := uc.goodsRepo.CacheGet(ctx, fmt.Sprintf("#goods-pageviews-%s", goodsId))
	if err != nil {
		return buyerCounter, err
	}
	pageViewsAmount, err := strconv.ParseFloat(amountStr, 64)
	if err != nil {
		return buyerCounter, err
	}
	// 4. 按照公式计算商品售卖数量
	if subjectId == "1" { // 数学
		buyerCounter = math.Round(base*0.5 + pageViewsAmount/50)
	} else if subjectId == "2" { // 物理
		buyerCounter = math.Round(base*0.3 + pageViewsAmount/50)
	} else if subjectId == "4" { // 化学
		buyerCounter = math.Round(base*0.15 + pageViewsAmount/50)
	} else { // 其他
		buyerCounter = math.Round(base*0.1 + pageViewsAmount/50)
	}
	return buyerCounter, err
}

func (uc *GoodsUseCase) getMaintainSchoolInfo(ctx context.Context, userId string) (isDeepMaintainSchool, isSchoolRoom bool, err error) {
	roomMemebers, err := uc.teacherThird.GetUserMembersWithRoom(ctx, []string{userId})
	if err != nil {
		return false, false, err
	}
	isSchoolRoom = lo.SomeBy(roomMemebers, func(m *teacher.MembersWithRoom) bool {
		return m.AdminRoom.Type == enum.ROOM_TYPE_SCHOOL
	})
	var schoolIds []string
	if !isSchoolRoom {
		// 只在教学班，需要从用户信息中获取学校信息
		me, err := uc.teacherUserThird.GetMe(ctx, userId)
		if err != nil {
			return false, false, err
		}
		if me.School.ID > 0 {
			schoolIds = append(schoolIds, me.School.ObjectID)
		}
	} else {
		schoolIds = lo.Map(roomMemebers, func(m *teacher.MembersWithRoom, _ int) string {
			return m.AdminRoom.SchoolId
		})
		schoolIds = lo.Filter(lo.Uniq(schoolIds), func(id string, _ int) bool {
			return len(id) > 0
		})
	}
	var schools []*channel.MaintainSchool
	if len(schoolIds) > 0 {
		schools, err = uc.channelThird.GetMaintainSchoolsBySchoolIds(ctx, schoolIds)
		if err != nil {
			return false, false, err
		}
	}
	isDeepMaintainSchool = lo.SomeBy(schools, func(s *channel.MaintainSchool) bool {
		return s.IsDeepOperation
	})
	return isDeepMaintainSchool, isSchoolRoom, nil
}

func (uc *GoodsUseCase) GetSkuGroupGoodsList(ctx context.Context, groups []string, ids []string) ([]SkuGroup, error) {
	list, err := uc.orderThird.GetSkuGroupGoodsList(ctx, groups, ids)
	if err != nil {
		return nil, err
	}
	var res []SkuGroup
	err = copier.Copy(&res, &list)
	if err != nil {
		return nil, err
	}
	return res, err
}

func (uc *GoodsUseCase) CreateGoodsBySkuGroup(ctx context.Context, req CreateGoodsBySkuGroupParams) (*Goods, error) {
	params := order.CreateGoodsBySkuGroupParams{}
	err := copier.Copy(&params, &req)
	if err != nil {
		return nil, err
	}
	goods, err := uc.orderThird.CreateGoodsBySkuGroup(ctx, params)
	if err != nil {
		return nil, err
	}
	res := Goods{}
	err = copier.Copy(&res, &goods)
	if err != nil {
		return nil, err
	}
	return &res, nil
}

func (uc *GoodsUseCase) GetGoodList(ctx context.Context, req *pb.GetGoodListReq) (*pb.GetGoodListReply, error) {
	userId := req.UserId
	isNormalGoods := req.IsNormalGoods
	roomMembers, err := uc.teacherThird.GetUserMembersWithRoom(ctx, []string{userId})
	if err != nil {
		return nil, err
	}
	isSchoolRoom := lo.SomeBy(roomMembers, func(m *teacher.MembersWithRoom) bool {
		return m.AdminRoom.Type == enum.ROOM_TYPE_SCHOOL
	})
	var schoolIds []string
	if !isSchoolRoom {
		// 只在教学班，需要从用户信息中获取学校信息
		me, err := uc.teacherUserThird.GetMe(ctx, userId)
		if err != nil {
			return nil, err
		}
		if len(me.School.ObjectID) > 0 {
			schoolIds = append(schoolIds, me.School.ObjectID)
		}
	} else {
		schoolIds = lo.Map(roomMembers, func(m *teacher.MembersWithRoom, _ int) string {
			return m.AdminRoom.SchoolId
		})
		schoolIds = lo.Filter(lo.Uniq(schoolIds), func(s string, _ int) bool {
			return len(s) > 0
		})
	}
	var schools []*channel.MaintainSchool
	if len(schoolIds) > 0 {
		schools, err = uc.channelThird.GetMaintainSchoolsBySchoolIds(ctx, schoolIds)
		if err != nil {
			return nil, err
		}
	}
	isMaintainSchool := len(schools) > 0
	isDeepOperationSchool := lo.SomeBy(schools, func(s *channel.MaintainSchool) bool {
		return s.IsDeepOperation
	})
	if isNormalGoods {
		schoolStudentAppGoods, err := uc.orderThird.GetFormatGoodList(ctx, enum.GOODS_GROUP_STUDENT, nil, false, false)
		if err != nil {
			return nil, err
		}
		schoolStudentAppGoods = orderGoods(schoolStudentAppGoods)
		schoolStudentAppGoods = lo.Filter(schoolStudentAppGoods, func(g *order.FormatGoods, _ int) bool {
			return isShowGoodsBySchoolAndRoom(g, isMaintainSchool, isDeepOperationSchool, isSchoolRoom)
		})
		var rows []*pb.GoodsItem
		err = copier.Copy(&rows, schoolStudentAppGoods)
		if err != nil {
			return nil, err
		}
		return &pb.GetGoodListReply{Rows: rows}, nil
	}
	roomIDs := lo.Map(roomMembers, func(m *teacher.MembersWithRoom, _ int) string {
		return m.AdminRoom.Id
	})
	roomIDs = append(roomIDs, lo.Map(roomMembers, func(m *teacher.MembersWithRoom, _ int) string {
		return m.AdminRoom.ID_
	})...)
	groupBuyingGoods, err := uc.getUserGroupBuyingGoods(ctx, roomIDs)
	if err != nil {
		return nil, err
	}
	specialCourseList, err := uc.crsThird.GetSpecialCourseList(ctx)
	specialCourseMap := map[string]crs.SpecialCourse{}
	for i := 0; i < len(specialCourseList); i++ {
		s := specialCourseList[i]
		specialCourseMap[s.Id] = s
	}
	schoolStudentAppGoods, err := uc.orderThird.GetFormatGoodList(ctx, enum.GOODS_GROUP_STUDENT, nil, false, false)
	if err != nil {
		return nil, err
	}
	unGroupBuyingGoods := lo.Filter(schoolStudentAppGoods, func(good *order.FormatGoods, _ int) bool {
		return !lo.SomeBy(groupBuyingGoods, func(value *order.FormatGoods) bool {
			return !isMultiSubject(value, specialCourseMap).IsMulti && value.StageID == good.StageID && value.SubjectID == good.SubjectID
		}) && isShowGoodsBySchoolAndRoom(good, isMaintainSchool, isDeepOperationSchool, isSchoolRoom)
	})
	goods := orderGoods(append(groupBuyingGoods, unGroupBuyingGoods...))
	var goodsList []*order.FormatGoods
	for i := 0; i < len(goods); i++ {
		g := goods[i]
		multiInfo := isMultiSubject(g, specialCourseMap)
		if multiInfo.IsMulti {
			// 多科联售
			g.IsMultiSubject = true
		} else {
			g.IsMultiSubject = false
			if len(multiInfo.StageSubjects) > 0 {
				s := &multiInfo.StageSubjects[0]
				g.SubjectID = s.SubjectId
				g.StageID = s.StageId
			}
		}
		g.SkuList = nil
		goodsList = append(goodsList, g)
	}
	var rows []*pb.GoodsItem
	err = copier.Copy(&rows, goodsList)
	if err != nil {
		return nil, err
	}
	return &pb.GetGoodListReply{Rows: rows}, nil
}

func (uc *GoodsUseCase) getUserGroupBuyingGoods(ctx context.Context, roomIDs []string) ([]*order.FormatGoods, error) {
	roomIDs = lo.Filter(roomIDs, func(id string, _ int) bool {
		return id != ""
	})
	roomIDs = lo.Uniq(roomIDs)
	if len(roomIDs) == 0 {
		return nil, nil
	}
	groupBuyingValue, err := uc.channelThird.GetRoomsGroupBuying(ctx, roomIDs)
	if err != nil {
		return nil, err
	}
	if !groupBuyingValue.IsGroupBuying || len(*groupBuyingValue.Goods) == 0 {
		return nil, nil
	}
	goodsIDs := lo.Map(*groupBuyingValue.Goods, func(g channel.GroupBuyingGoods, _ int) string {
		return g.ID
	})
	goods, err := uc.orderThird.GetFormatGoodList(ctx, "", goodsIDs, true, false)
	if err != nil {
		return nil, err
	}
	goods = lo.Filter(goods, func(g *order.FormatGoods, _ int) bool {
		return lo.SomeBy(*groupBuyingValue.StageSubject, func(ss string) bool {
			return ss == fmt.Sprintf("%s-%s", g.StageID, g.SubjectID)
		})
	})
	return goods, nil
}

func orderGoods(goods []*order.FormatGoods) []*order.FormatGoods {
	hasEndTimeGoods := lo.Filter(goods, func(g *order.FormatGoods, _ int) bool {
		return g.EndTime != nil
	})
	sort.Slice(hasEndTimeGoods, func(i, j int) bool {
		return *hasEndTimeGoods[i].EndTime < *hasEndTimeGoods[j].EndTime
	})
	noEndTimeGoods := lo.Filter(goods, func(g *order.FormatGoods, _ int) bool {
		return g.EndTime == nil
	})
	sort.Slice(noEndTimeGoods, func(i, j int) bool {
		return noEndTimeGoods[i].Duration > noEndTimeGoods[j].Duration
	})
	goods = append(hasEndTimeGoods, noEndTimeGoods...)
	return goods
}

func isShowGoodsBySchoolAndRoom(goods *order.FormatGoods, isMaintainSchool, isDeepOperationSchool, isSchoolRoom bool) bool {
	if len(goods.SkuList) == 0 {
		return false
	}
	duration := goods.SkuList[0].Sku.Distributor.Params.AddTime
	if checkSkuTimeAndSchool(cast.ToInt(duration), isMaintainSchool, isDeepOperationSchool, isSchoolRoom) {
		return true
	}
	for i := 0; i < len(goods.SkuList); i++ {
		dis := goods.SkuList[i].Sku.Distributor
		if dis.Kind == "specialCourse" {
			if enum.CheckSpecialCourseID(dis.Params.Id) {
				continue
			}
		}
		if !enum.CheckAuthKind(dis.Kind) {
			continue
		}
		duration = goods.SkuList[i].Sku.Distributor.Params.AddTime
		if checkSkuTimeAndSchool(cast.ToInt(duration), isMaintainSchool, isDeepOperationSchool, isSchoolRoom) {
			continue
		}
		return false
	}
	return true
}

func checkSkuTimeAndSchool(duration int, isMaintainSchool, isDeepOperationSchool, isSchoolRoom bool) bool {
	month := 1000 * 60 * 60 * 24 * 31
	if isDeepOperationSchool {
		// 深度维护校，只显示 12 个月商品
		return duration == month*12
	}
	if isMaintainSchool && isSchoolRoom {
		// 维护校行政班，只显示 3、6、12 个月的商品
		return duration == month*3 || duration == month*6 || duration == month*12
	}
	return false
}

func isMultiSubject(goods *order.FormatGoods, specialCourseMap map[string]crs.SpecialCourse) IsMultiSubjectRes {
	physicalSkuKindMap := map[string]bool{
		"textbook":          true,
		"derivativeProduct": true,
		"pad":               true,
	}
	skuList := lo.Filter(goods.SkuList, func(sku *order.SkuItem, _ int) bool {
		return sku != nil && sku.Sku != nil && sku.Sku.Distributor != nil && !physicalSkuKindMap[sku.Sku.Distributor.Kind]
	})
	if len(skuList) == 0 {
		return IsMultiSubjectRes{}
	}
	var stageSubjects []MultiStageSubject
	for i := 0; i < len(skuList); i++ {
		sku := skuList[i]
		if sku.Sku.Distributor.Kind == "specialCourse" {
			id := sku.Sku.Distributor.Params.Id
			special, ok := specialCourseMap[id]
			if !ok {
				continue
			}
			stageSubjects = append(stageSubjects, specialCourseStageSubject(special)...)
		} else if sku.Sku != nil && sku.Sku.Distributor != nil && sku.Sku.Distributor.Params != nil && sku.Sku.Distributor.Params.Stage != "" && sku.Sku.Distributor.Params.Subject != "" {
			stageSubjects = append(stageSubjects, MultiStageSubject{
				StageId:   sku.Sku.Distributor.Params.Stage,
				SubjectId: sku.Sku.Distributor.Params.Subject,
			})
		}
	}
	stageSubjects = lo.UniqBy(stageSubjects, func(ss MultiStageSubject) string {
		return fmt.Sprintf("%s-%s", ss.StageId, ss.SubjectId)
	})
	return IsMultiSubjectRes{
		IsMulti:       len(stageSubjects) > 1,
		StageSubjects: stageSubjects,
	}
}

func specialCourseStageSubject(course crs.SpecialCourse) []MultiStageSubject {
	var res []MultiStageSubject
	for i := 0; i < len(course.StageIds); i++ {
		stageId := course.StageIds[i]
		for j := 0; j < len(course.SubjectIds); j++ {
			subjectId := course.SubjectIds[j]
			res = append(res, MultiStageSubject{
				StageId:   cast.ToString(stageId),
				SubjectId: cast.ToString(subjectId),
			})
		}
	}
	return res
}

func (uc *GoodsUseCase) GetGroupBuyingInfo(ctx context.Context, userID string) (*channel.GroupBuying, error) {
	roomMembers, err := uc.teacherThird.GetUserMembersWithRoom(ctx, []string{userID})
	if err != nil {
		return nil, err
	}
	isSchoolRoom := lo.SomeBy(roomMembers, func(m *teacher.MembersWithRoom) bool {
		return m.AdminRoom.Type == enum.ROOM_TYPE_SCHOOL
	})
	var schoolIds []string
	if !isSchoolRoom {
		// 只在教学班，需要从用户信息中获取学校信息
		me, err := uc.teacherUserThird.GetMe(ctx, userID)
		if err != nil {
			return nil, err
		}
		if len(me.School.ObjectID) > 0 {
			schoolIds = append(schoolIds, me.School.ObjectID)
		}
	} else {
		schoolIds = lo.Map(roomMembers, func(m *teacher.MembersWithRoom, _ int) string {
			return m.AdminRoom.SchoolId
		})
		schoolIds = lo.Filter(lo.Uniq(schoolIds), func(s string, _ int) bool {
			return len(s) > 0
		})
	}
	roomIDs := lo.Map(roomMembers, func(m *teacher.MembersWithRoom, _ int) string {
		return m.AdminRoom.Id
	})
	roomIDs = append(roomIDs, lo.Map(roomMembers, func(m *teacher.MembersWithRoom, _ int) string {
		return m.AdminRoom.ID_
	})...)
	groupBuying, err := uc.channelThird.GetRoomsGroupBuying(ctx, roomIDs)
	return &groupBuying, err
}

func (uc *GoodsUseCase) CheckCanBuyGoodsBySchool(ctx context.Context, req *pb.CheckCanBuyGoodsBySchoolReq) (bool, error) {
	// 1. 检查goodKindIdLevel2是否在checkSchoolGoodKindIdLevel2列表中，如果不在，直接返回允许购买
	if !lo.Contains(uc.bootstrapConfig.CheckSchoolGoodKindIdLevel2, req.GoodKindIdLevel2) {
		return true, nil
	}
	// 2. 用userId通过usercore下面的接口获取用户详情，如果用户为c，则直接返回不允许购买
	users, err := uc.userCoreThird.BatchQueryUsers(ctx, "id", []string{req.UserId})
	if err != nil {
		return false, err
	}
	if len(users) == 0 {
		return false, httpErrors.BadRequest("用户不存在", "用户不存在")
	}
	user := users[0]
	if strings.ToLower(user.Attribution) == "c" {
		return false, nil
	}
	if user.SchoolId == 0 {
		return false, nil
	}
	// 3. 检查学生是否在行政班，以及行政班所在学校是否一致
	rooms, err := uc.teacherThird.GetStudentRoomBaseInfos(ctx, req.UserId)
	if err != nil {
		return false, err
	}
	adminRooms := lo.Filter(rooms, func(r teacher.RoomBase, _ int) bool {
		return r.Type == enum.ROOM_TYPE_SCHOOL
	})
	if len(adminRooms) == 0 {
		// 不在行政班
		return false, nil
	}
	s, err := uc.schoolThird.GetSchoolInfo(ctx, cast.ToString(user.SchoolId))
	if err != nil {
		return false, err
	}
	roomInSchool := lo.ContainsBy(adminRooms, func(r teacher.RoomBase) bool {
		return r.SchoolId == s.MogoId
	})
	if !roomInSchool {
		return false, nil
	}
	// 4. 根据用户详情的学校id判断商品是否在代理商品进行中允许范围内，在进行中范围内且未屏蔽禁用则返回可购买否则不允许购买
	agencyGoods, err := uc.agencyG
	if err != nil {
		uc.log.Errorf("FindAgencyGoodsByUserID error: %v", err)
		return false, nil
	}

	// 检查商品是否在代理商品列表中
	goodId := req.GoodId
	if goodId == "" {
		goodId = req.SkuGroupGoodId
	}

	for _, agencyGood := range agencyGoods {
		var detail adminBiz.AgencyPaymentGoodDetail
		if err := agencyGood.GoodsInfo.AssignTo(&detail); err != nil {
			continue
		}

		if detail.ID == goodId {
			return true, nil
		}
	}

	return false, nil
}

func (uc *GoodsUseCase) GoodsDetailByID(ctx context.Context, userID, goodsID string) (*pb.GoodsDetailByIDReply, error) {
	groupBuying, err := uc.GetGroupBuyingInfo(ctx, userID)
	if err != nil {
		return nil, err
	}

	g, statusCode, err := uc.orderThird.GetGoodsById(ctx, goodsID)
	if err != nil {
		uc.log.Errorf("get goods by id error: %v, statusCode: %d", err, statusCode)
		return nil, httpErrors.New(statusCode, "", "获取商品失败")
	}
	var detail pb.GoodItem
	err = copier.Copy(&detail, g)
	if err != nil {
		return nil, err
	}
	if groupBuying.Goods != nil {
		g, exist := lo.Find(*groupBuying.Goods, func(item channel.GroupBuyingGoods) bool {
			return item.Goods.ID == g.ID
		})
		detail.IsBulk = exist
		if exist {
			detail.EntryId = g.EntryId
		}
	}
	return &pb.GoodsDetailByIDReply{Data: &detail}, nil
}
func (uc *GoodsUseCase) GoodsMergeCoupon(ctx context.Context, goodsID, name string) (*pb.GoodsDetailByIDReply, error) {
	if name == "" {
		g, statusCode, err := uc.orderThird.GetGoodsById(ctx, goodsID)
		if err != nil {
			uc.log.Errorf("get goods by id error: %v, statusCode: %d", err, statusCode)
			return nil, httpErrors.New(statusCode, "", "获取商品失败")
		}
		name = g.Name
	}
	couponGoodsID := uc.goodsRepo.GetSavingsCardGoodsID()
	newGood, err := uc.goodThird.GoodMerge(ctx, goodsID, couponGoodsID, name)
	if err != nil {
		return nil, err
	}
	var res pb.GoodItem
	err = copier.Copy(&res, newGood)
	if err != nil {
		return nil, err
	}
	return &pb.GoodsDetailByIDReply{Data: &res}, nil
}

func (uc *GoodsUseCase) GetTeacherGoodsList(ctx context.Context) ([]*TeacherGoodInfo, error) {
	goodList, err := uc.goodThird.GetAllCombinationGoods(ctx, good.GetCombinationGoodsListReq{GroupList: []string{"teacherapp"}, StatusList: []string{"已上架"}})
	if err != nil {
		return nil, err
	}
	topicsMap := map[string]int{
		"1-1":  400,
		"2-1":  600,
		"2-2":  600,
		"2-4":  100,
		"2-3":  50,
		"2-5":  100,
		"2-6":  30,
		"3-1":  400,
		"1-10": 200,
	}
	combineGoodIDs := lo.Map(goodList, func(g *good.CombinationGoods, _ int) string {
		return g.Id
	})
	normalGoodChunks := make([][]string, len(combineGoodIDs))
	var g errgroup.Group
	g.SetLimit(4)
	for i := 0; i < len(combineGoodIDs); i++ {
		id := combineGoodIDs[i]
		idx := i
		g.Go(func() error {
			normal, gErr := uc.goodThird.CreateNormalByCombination(ctx, &skuGood.CreateNormalGoodReq{Id: id})
			if gErr != nil {
				return gErr
			}
			normalGoodChunks[idx] = []string{id, normal.Id}
			return nil
		})
	}
	err = g.Wait()
	if err != nil {
		return nil, err
	}
	normalGoodMap := make(map[string]string)
	for i := 0; i < len(normalGoodChunks); i++ {
		ids := normalGoodChunks[i]
		if len(ids) != 2 {
			continue
		}
		id, normalId := ids[0], ids[1]
		if id == "" || normalId == "" {
			continue
		}
		normalGoodMap[id] = normalId
	}
	res := lo.Map(goodList, func(g *good.CombinationGoods, _ int) *TeacherGoodInfo {
		if g == nil || len(g.RequiredItemList) == 0 {
			return nil
		}
		// 约定商品只有一个必须sku
		item := g.RequiredItemList[0]
		if len(item.ItemDetailList) == 0 {
			return nil
		}
		sku := item.ItemDetailList[0]
		if sku.SkuSingleDetail == nil {
			return nil
		}
		detail := sku.SkuSingleDetail
		topicCount := topicsMap[fmt.Sprintf("%s-%s", detail.Stage, detail.Subject)]
		if topicCount == 0 {
			topicCount = 100
		}
		normalID, ok := normalGoodMap[g.Id]
		if !ok {
			return nil
		}
		return &TeacherGoodInfo{
			Id:             normalID,
			Id_:            normalID,
			Name:           g.Name,
			Description:    "",
			OriginalAmount: g.OriginalAmount,
			Amount:         g.Amount,
			SubjectId:      detail.Subject,
			StageId:        detail.Stage,
			TopicTotal:     topicCount,
		}
	})
	res = lo.Filter(res, func(g *TeacherGoodInfo, _ int) bool {
		return g != nil
	})
	sort.Slice(res, func(i, j int) bool {
		if res[i].StageId != res[j].StageId {
			return res[i].StageId < res[j].StageId
		}
		return res[i].SubjectId < res[j].SubjectId
	})
	return res, nil
}
