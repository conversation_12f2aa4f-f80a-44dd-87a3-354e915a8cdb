package service

import (
	"context"
	"fmt"
	"strings"
	"teacher-mall/internal/common/enum"
	"teacher-mall/internal/mall/goods/biz"

	_struct "github.com/golang/protobuf/ptypes/struct"
	"google.golang.org/protobuf/types/known/structpb"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/metadata"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/jinzhu/copier"
	"github.com/samber/lo"

	"net/http"
	pb "teacher-mall/api/mall/goods"
	"time"

	"encoding/json"
	"gopkg.in/mgo.v2/bson"
)

type GoodsService struct {
	pb.UnimplementedGoodsServer
	uc *biz.GoodsUseCase
}

func NewGoodsService(uc *biz.GoodsUseCase) *GoodsService {
	return &GoodsService{
		uc: uc,
	}
}

func (s *GoodsService) IsRepeatPayGoodId(ctx context.Context, req *pb.IsRepeatPayGoodIdReq) (*pb.IsRepeatPayGoodIdReply, error) {
	if req.UserId == "" {
		return nil, errors.BadRequest("无权访问", "无权访问")
	}
	isRepeat, err := s.uc.IsRepeatPayGoodId(ctx, req)
	if err != nil {
		return nil, err
	}
	return &pb.IsRepeatPayGoodIdReply{
		IsRepeat: isRepeat,
	}, nil
}

func (s *GoodsService) GetCombinationGoodsExtraInfo(ctx context.Context, req *pb.GetCombinationGoodsInfoReq) (*structpb.Struct, error) {
	var userID string
	if md, ok := metadata.FromServerContext(ctx); ok {
		userID = md.Get(enum.X_MD_GLOBAL_UID)
	}
	if userID == "" {
		return nil, errors.BadRequest("无权访问", "无权访问")
	}
	return s.uc.GetCombinationGoodsDetailByID(ctx, userID, req.GoodsId)
}
func (s *GoodsService) GetStageSubjects(ctx context.Context, req *pb.GetStageSubjectsRequest) (*pb.GetStageSubjectsReply, error) {
	var res pb.GetStageSubjectsReply
	var list []*pb.StageSubject
	ss, err := s.uc.GetStageSubjectList(ctx)
	if err != nil {
		return &res, err
	}
	err = copier.Copy(&list, ss)
	if err != nil {
		return &res, err
	}
	res.Records = list
	return &res, nil
}

func (s *GoodsService) GetGoodsView(ctx context.Context, req *pb.GetGoodsViewReq) (*pb.GetGoodsViewReply, error) {
	var userId string
	if md, ok := metadata.FromServerContext(ctx); ok {
		userId = md.Get(enum.X_MD_GLOBAL_UID)
	}
	var clientType []string
	if v, ok := ctx.Value("Client-Type").([]string); ok {
		clientType = v
	}
	//  := cast.ToString(ctx.Value("Client-Type"))
	var res pb.GetGoodsViewReply
	goodsIdList := strings.Split(req.GoodsId, ",")
	goodsIdList = lo.Filter(goodsIdList, func(id string, _ int) bool {
		return strings.TrimSpace(id) != ""
	})
	groupList := strings.Split(req.GoodsGroup, ",")
	groupList = lo.Filter(groupList, func(g string, _ int) bool {
		return strings.TrimSpace(g) != ""
	})
	viewId := req.ViewId
	if req.ViewType == "system" && req.StageId > 0 && req.SubjectId > 0 {
		viewId = fmt.Sprintf("%s_%d_%d", req.ViewType, req.StageId, req.SubjectId)
	}
	goodsView, err := s.uc.GetGoodsView(ctx, userId, viewId, groupList, goodsIdList, req.StageId, req.SubjectId)
	if err != nil {
		return &res, err
	}
	if req.GoodsGroup == enum.GOODS_GROUP_STUDENT {
		// 正价商品，需要根据客户端类型进行过滤，ios不显示不支持ios支付平台的商品，安卓不显示只有ios支付的商品
		if len(clientType) > 0 && lo.Contains(clientType, enum.CLIENT_TYPE_IOS_APP) {
			goodsView.GoodsList = lo.Filter(goodsView.GoodsList, func(g *biz.CombinationGoods, _ int) bool {
				return lo.Contains(g.PaymentPlatform, enum.GOODS_PAYMENT_PLATFORM_IOS)
			})
		} else if len(clientType) > 0 && lo.Contains(clientType, enum.CLIENT_TYPE_ANDROID_APP) {
			goodsView.GoodsList = lo.Filter(goodsView.GoodsList, func(g *biz.CombinationGoods, _ int) bool {
				return !(lo.Contains(g.PaymentPlatform, enum.GOODS_PAYMENT_PLATFORM_IOS) && len(g.PaymentPlatform) == 1)
			})
		}
	}

	err = copier.Copy(&res, goodsView)
	return &res, err
}

func (s *GoodsService) GetGroupGoodsList(ctx context.Context, req *pb.GetGroupGoodsListReq) (*pb.GetGroupGoodsListReply, error) {
	var userId string
	if md, ok := metadata.FromServerContext(ctx); ok {
		userId = md.Get(enum.X_MD_GLOBAL_UID)
	}
	var res *pb.GetGroupGoodsListReply
	var items []*pb.GoodItem
	goodsList, expireDate, err := s.uc.GetGroupGoodsList(ctx, userId)
	if err != nil {
		return res, err
	}
	err = copier.Copy(&items, goodsList)
	if err != nil {
		return res, err
	}
	res = new(pb.GetGroupGoodsListReply)
	for i := 0; i < len(items); i++ {
		items[i].IsBulk = true
	}
	res.Records = items
	if expireDate != nil {
		res.ExpireDate = timestamppb.New(*expireDate)
	}
	return res, err
}

func (s *GoodsService) GetGoodsInfo(ctx context.Context, req *pb.GetGoodsInfoReq) (*pb.GetGoodsInfoReply, error) {
	var res pb.GetGoodsInfoReply
	goodsId := req.Id
	userId := req.UserId
	if md, ok := metadata.FromServerContext(ctx); ok || len(userId) == 0 {
		userId = md.Get(enum.X_MD_GLOBAL_UID)
	}
	if len(goodsId) == 0 {
		return nil, errors.BadRequest("", "id参数必须")
	}
	if req.To == "seeDetail" {
		_ = s.uc.PageViewIncr(ctx, goodsId)
	}
	goodsInfo, err := s.uc.GetGoodsInfo(ctx, goodsId, userId, req.RegionCode, req.To)
	if err != nil {
		return nil, err
	}
	err = copier.Copy(&res, goodsInfo)
	res.Skus = lo.Map(goodsInfo.Skus, func(sku *biz.SkuAmount, _ int) *_struct.Struct {
		r := _struct.Struct{}
		r.Fields = map[string]*structpb.Value{}
		r.Fields["name"] = &_struct.Value{Kind: &_struct.Value_StringValue{StringValue: sku.Name}}
		r.Fields["amount"] = &_struct.Value{Kind: &_struct.Value_NumberValue{NumberValue: float64(sku.Amount)}}
		r.Fields["originalAmount"] = &_struct.Value{Kind: &_struct.Value_NumberValue{NumberValue: float64(sku.OriginalAmount)}}
		r.Fields["discountedPrices"] = &_struct.Value{Kind: &_struct.Value_NumberValue{NumberValue: float64(sku.DiscountedPrices)}}
		r.Fields["isBulk"] = &_struct.Value{Kind: &_struct.Value_BoolValue{BoolValue: sku.IsBulk}}
		r.Fields["id"] = &_struct.Value{Kind: &_struct.Value_StringValue{StringValue: sku.Id}}
		r.Fields["addTime"] = &_struct.Value{Kind: &_struct.Value_NumberValue{NumberValue: float64(sku.AddTime)}}
		r.Fields["kind"] = &_struct.Value{Kind: &_struct.Value_StringValue{StringValue: sku.Kind}}
		r.Fields["stageId"] = &_struct.Value{Kind: &_struct.Value_StringValue{StringValue: sku.StageId}}
		r.Fields["subjectId"] = &_struct.Value{Kind: &_struct.Value_StringValue{StringValue: sku.SubjectId}}
		for k, v := range sku.Attributes {
			r.Fields[k] = &_struct.Value{Kind: &_struct.Value_StringValue{StringValue: v}}
		}
		return &r
	})
	return &res, err
}

func (s *GoodsService) GetSkuGroupGoodsList(ctx context.Context, req *pb.GetSkuGroupGoodsListReq) (*pb.GetSkuGroupGoodsListReply, error) {
	list, err := s.uc.GetSkuGroupGoodsList(ctx, req.Groups, req.Ids)
	if err != nil {
		return nil, err
	}
	var res []*pb.SkuGroup
	err = copier.Copy(&res, list)
	if err != nil {
		return nil, err
	}
	return &pb.GetSkuGroupGoodsListReply{Rows: res}, nil
}

func (s *GoodsService) CreateGoodsBySkuGroup(ctx context.Context, req *pb.CreateGoodsBySkuGroupReq) (*pb.GoodItem, error) {
	params := biz.CreateGoodsBySkuGroupParams{}
	err := copier.Copy(&params, req)
	if err != nil {
		return nil, err
	}
	goods, err := s.uc.CreateGoodsBySkuGroup(ctx, params)
	if err != nil {
		return nil, err
	}
	res := pb.GoodItem{}
	err = copier.Copy(&res, &goods)
	if err != nil {
		return nil, err
	}
	return &res, nil
}

func (s *GoodsService) GetGoodList(ctx context.Context, req *pb.GetGoodListReq) (*pb.GetGoodListReply, error) {
	return s.uc.GetGoodList(ctx, req)
}

func (s *GoodsService) GoodsDetailByID(ctx context.Context, req *pb.GoodsDetailByIDReq) (*pb.GoodsDetailByIDReply, error) {
	var userID string
	if md, ok := metadata.FromServerContext(ctx); ok {
		userID = md.Get(enum.X_MD_GLOBAL_UID)
	}
	if !bson.IsObjectIdHex(req.GoodsId) {
		return nil, errors.BadRequest("", "goodsId参数错误")
	}
	return s.uc.GoodsDetailByID(ctx, userID, req.GoodsId)
}

func (s *GoodsService) GoodsMergeCoupon(ctx context.Context, req *pb.GoodsMergeCouponReq) (*pb.GoodsDetailByIDReply, error) {
	downAt := time.Date(2024, time.August, 1, 0, 0, 0, 0, time.Local)
	if time.Now().After(downAt) {
		return nil, errors.New(400, "功能已下线", "功能已下线")
	}
	if req.GoodsId == "" {
		return nil, errors.New(http.StatusBadRequest, "", "商品id不能为空")
	}
	return s.uc.GoodsMergeCoupon(ctx, req.GoodsId, req.Name)
}

func (s *GoodsService) GetTeacherGoodsList(ctx context.Context, req *pb.GetTeacherGoodsListReq) (*structpb.ListValue, error) {
	goodList, err := s.uc.GetTeacherGoodsList(ctx)
	if err != nil {
		return nil, err
	}
	bys, err := json.Marshal(goodList)
	if err != nil {
		return nil, err
	}
	var res structpb.ListValue
	err = res.UnmarshalJSON(bys)
	if err != nil {
		return nil, err
	}
	return &res, nil
}

func (s *GoodsService) CheckCanBuyGoodsBySchool(ctx context.Context, req *pb.CheckCanBuyGoodsBySchoolReq) (*pb.CheckCanBuyGoodsBySchoolRes, error) {
	// 1. userId、两个商品id以及goodKindIdLevel2为空则返回错误提示
	if req.UserId == "" || (req.GoodId == "" && req.SkuGroupGoodId == "") || req.GoodKindIdLevel2 == "" {
		return nil, errors.BadRequest("参数错误", "参数错误")
	}
	canBuy, err := s.uc.CheckCanBuyGoodsBySchool(ctx, req)
	if err != nil {
		return nil, err
	}
	return &pb.CheckCanBuyGoodsBySchoolRes{
		CanBuy: canBuy,
	}, nil
}
