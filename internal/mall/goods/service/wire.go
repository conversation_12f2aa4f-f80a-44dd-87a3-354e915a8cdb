//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package service

import (
	adminGoodsData "teacher-mall/internal/admin/goods/data"
	"teacher-mall/internal/conf"
	"teacher-mall/internal/mall/biz"
	mallGoodsBiz "teacher-mall/internal/mall/goods/biz"
	"teacher-mall/internal/mall/goods/data"
	"teacher-mall/internal/pkg/db"
	thirdparty "teacher-mall/third_party"
	"teacher-mall/third_party/channel"
	"teacher-mall/third_party/crs"
	"teacher-mall/third_party/good"
	"teacher-mall/third_party/order"
	"teacher-mall/third_party/qiniu"
	"teacher-mall/third_party/school"
	"teacher-mall/third_party/teacher"
	"teacher-mall/third_party/teacheruser"
	"teacher-mall/third_party/teachingresource"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

func WireMallOrder(*conf.Server, *conf.Data, log.Logger) (*GoodsService, func(), error) {
	panic(wire.Build(
		db.ProviderSet,
		thirdparty.ProviderSet,
		mallGoodsBiz.ProviderSet,
		channel.ProviderSet,
		order.ProviderSet,
		teacher.ProviderSet,
		data.ProviderSet,
		teachingresource.ProviderSet,
		qiniu.ProviderSet,
		biz.ProviderSet,
		adminGoodsData.ProviderSet,
		teacheruser.ProviderSet,
		crs.ProviderSet,
		good.ProviderSet,
		school.ProviderSet,
		// discount.ProviderSet,
		ProviderSet,
	))
}
