syntax = "proto3";
package kratos.api;

option go_package = "teacher-mall/internal/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Data data = 2;
  repeated string checkSchoolGoodKindIdLevel2 = 3;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  HTTP http = 1;
  GRPC grpc = 2;
  string jwtSecret = 3;
}

message Data {
  message Database {
    string name = 1;
    string host = 2;
    string user = 3;
    string password = 4;
    int32 port = 5;
  }
  message Braavosdb {
    string name = 1;
    string host = 2;
    string user = 3;
    string password = 4;
    int32 port = 5;
  }
  message ScoreDb {
    string name = 1;
    string host = 2;
    string user = 3;
    string password = 4;
    int32 port = 5;
  }
  message Redis {
    string addr = 2;
    int32 db = 3;
    string password = 6;
    google.protobuf.Duration read_timeout = 4;
    google.protobuf.Duration write_timeout = 5;
    google.protobuf.Duration dial_timeout = 7;
    string keyPrefix = 8;
    int32 poolSize = 9;
    int32 minIdleConns = 10;
  }
  message Nacos {
    string host = 1;
    uint64 port = 2;
    string username = 3;
    string password = 4;
  }
  message Qiniu {
    string domain = 1;
    string ak = 2;
    string sk = 3;
    string bucket = 4;
    string zone = 5;
  }
  message Ycoss {
    string accessToken = 1;
    string bucket = 2;
  }
  Database database = 1;
  Database channelDb = 2;
  Redis redis = 3;
  Nacos nacos = 4;
  Braavosdb braavosdb = 5;
  ScoreDb scoreDb = 6;
  Qiniu qiniu = 7;
  string goodsPageDataDir = 8;
  Ycoss ycoss = 9;
  string  savingsCardGoodsId = 10;
}