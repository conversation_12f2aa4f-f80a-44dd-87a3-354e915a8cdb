package school

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-resty/resty/v2"
	"github.com/google/wire"
)

var ProviderSet = wire.NewSet(NewSchoolThird)

type schoolThird struct {
	client *resty.Client
}

func NewSchoolThird(client *resty.Client) ISchoolThird {
	return &schoolThird{
		client: client,
	}
}

func (s *schoolThird) GetSchoolInfo(ctx context.Context, id string) (res *School, err error) {
	url := fmt.Sprintf("http://school.teacherschool/school/%s", id)
	var resp *resty.Response
	client := s.client.R().SetContext(ctx)
	resp, err = client.SetResult(&res).Get(url)
	if err != nil {
		return
	}
	if resp.StatusCode() >= 400 {
		status := errors.Status{}
		_ = json.Unmarshal(resp.Body(), &status)
		err = errors.New(resp.StatusCode(), status.Reason, status.Message)
		return
	}
	return
}
