package school

type School struct {
	ID                 string `json:"id"`
	Name               string `json:"name"`
	RegionCode         string `json:"regionCode"`
	HeadLetter         string `json:"headLetter"`
	StageId            int32  `json:"stageId"`
	CreateTime         string `json:"createTime"`
	MogoId             string `json:"_id"`
	Deleted            bool   `json:"deleted"`
	GeoLocation        string `json:"geoLocation"`
	Location           string `json:"location"`
	Region0            string `json:"region0"`
	Region1            string `json:"region1"`
	ProvinceRegionCode string `json:"provinceRegionCode"`
	Category           string `json:"category"`
}
