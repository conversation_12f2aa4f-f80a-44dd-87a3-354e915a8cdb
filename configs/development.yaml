server:
  http:
    addr: 0.0.0.0:3000
    timeout: 60s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 60s
  jwtSecret: super MUZH
data:
  database:
    name: teacher-mall
    host: **********
    user: postgres
    password: teacherschoolpg94
    port: 5436
  channelDb:
    name: channel
    host: **********
    user: postgres
    password: teacherschoolpg94
    port: 5436
  braavosdb:
    name: braavos
    host: **********
    user: postgres
    password: teacherschoolpg94
    port: 5436
  scoreDb:
    name: daenerysTargaryen
    host: **********
    user: postgres
    password: teacherschoolpg94
    port: 5436
  redis:
    addr: **********:6379
    password: ""
    db: 0
    dial_timeout: 1s
    read_timeout: 0.4s
    write_timeout: 0.6s
    keyPrefix: "braavos:"
  nacos:
    host: nacos.ops
    port: 8848
    username: nacos
    password: unitednac
  qiniu:
    domain: https://cert-test.yc345.tv
    ak: zxFWN9Xq4SymaPblFMmQ-2WdBHcEotOiC0023cUJ
    sk: N03Youh6HVloq7F5r4T8r3awgqC1JvCId7-A6qBi
    bucket: yc-test
    zone": Zone_z0
  ycoss:
    accessToken: dfd17283-2e23-46bb-9762-92a959ebba7e
    bucket: school-res
  savingsCardGoodsId: 6669208f95abc36e1f664c18
checkSchoolGoodKindIdLevel2:
  - 40f2cc08-6c9d-45b5-90df-b26326c557a7
  - 967e062e-91c3-4ac6-b3bd-e4a244a1d2ff
  - cdab8fb8-3c51-41de-9887-1f98fa5b7615
  - 2bcf338e-5dc5-4d87-b878-f640700d3552
  - de09a43f-1c54-4a69-8573-84f340bfebd9
  - b14b01f6-2d44-4720-826a-5dd6b33a3ed6